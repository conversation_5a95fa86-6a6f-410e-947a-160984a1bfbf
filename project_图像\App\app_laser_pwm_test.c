// Copyright (c) 2024 白蛋电子工作室

#include "app_laser_draw.h"

/**
 * @brief 激光PWM控制测试函数
 */
void laser_pwm_test(void)
{
    // 初始化激光绘图系统
    app_laser_draw_init();
    
    // 测试不同PWM值
    for (uint16_t power = 0; power <= 100; power += 10)
    {
        // 设置激光功率
        laser_set_power(power);
        
        // 延时观察效果
        HAL_Delay(500);
    }
    
    // 关闭激光
    laser_off();
    
    // 测试激光开关
    for (int i = 0; i < 5; i++)
    {
        laser_on();   // 开启激光(PWM值=10)
        HAL_Delay(200);
        
        laser_off();  // 关闭激光(PWM值=0)
        HAL_Delay(200);
    }
}

/**
 * @brief 激光PWM渐变测试
 */
void laser_pwm_fade_test(void)
{
    // 初始化激光绘图系统
    app_laser_draw_init();
    
    // 渐亮测试
    for (uint16_t power = 0; power <= 999; power += 10)
    {
        laser_set_power(power);
        HAL_Delay(10);
    }
    
    // 渐暗测试
    for (uint16_t power = 999; power > 0; power -= 10)
    {
        laser_set_power(power);
        HAL_Delay(10);
    }
    
    // 关闭激光
    laser_off();
}
