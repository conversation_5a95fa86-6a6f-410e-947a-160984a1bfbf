// Copyright (c) 2024 白蛋电子工作室

#include "app_key_test.h"

/**
 * @brief 按键功能测试
 */
void key_function_test(void)
{
    // 测试各个按键的功能
    if (is_key_pressed(KEY1_PRESSED))
    {
        my_printf(&huart1, "KEY1 Function: Start Laser Drawing\r\n");
        // 可以调用激光绘图功能
        // app_laser_draw_init();
    }
    
    if (is_key_pressed(KEY2_PRESSED))
    {
        my_printf(&huart1, "KEY2 Function: Stop Current Operation\r\n");
        // 可以调用停止功能
        // stop_drawing();
    }
    
    if (is_key_pressed(KEY3_PRESSED))
    {
        my_printf(&huart1, "KEY3 Function: Toggle LED\r\n");
        // 切换LED状态
        static uint8_t led_state = 0;
        led_state = !led_state;
        if (led_state)
        {
            HAL_GPIO_WritePin(LED4_GPIO_Port, LED4_Pin, GPIO_PIN_RESET); // 点亮LED4
        }
        else
        {
            HAL_GPIO_WritePin(LED4_GPIO_Port, LED4_Pin, GPIO_PIN_SET);   // 熄灭LED4
        }
    }
    
    if (is_key_pressed(KEY4_PRESSED))
    {
        my_printf(&huart1, "KEY4 Function: System Reset\r\n");
        // 可以调用系统复位或其他功能
        // HAL_NVIC_SystemReset();
    }
}

/**
 * @brief 按键状态显示测试
 */
void key_status_display_test(void)
{
    static uint32_t last_display_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 每1秒显示一次按键状态
    if (current_time - last_display_time >= 1000)
    {
        last_display_time = current_time;
        
        my_printf(&huart1, "Key Status: KEY1=%d, KEY2=%d, KEY3=%d, KEY4=%d\r\n",
                  get_key_state(KEY1_PRESSED) ? 1 : 0,
                  get_key_state(KEY2_PRESSED) ? 1 : 0,
                  get_key_state(KEY3_PRESSED) ? 1 : 0,
                  get_key_state(KEY4_PRESSED) ? 1 : 0);
    }
}

/**
 * @brief 按键组合功能测试
 */
void key_combination_test(void)
{
    // 检测按键组合
    if (get_key_state(KEY1_PRESSED) && get_key_state(KEY2_PRESSED))
    {
        if (is_key_pressed(KEY1_PRESSED) || is_key_pressed(KEY2_PRESSED))
        {
            my_printf(&huart1, "Key Combination: KEY1 + KEY2 Pressed\r\n");
            // 执行组合功能
        }
    }
    
    if (get_key_state(KEY3_PRESSED) && get_key_state(KEY4_PRESSED))
    {
        if (is_key_pressed(KEY3_PRESSED) || is_key_pressed(KEY4_PRESSED))
        {
            my_printf(&huart1, "Key Combination: KEY3 + KEY4 Pressed\r\n");
            // 执行组合功能
        }
    }
}

/**
 * @brief 按键长按检测测试
 */
void key_long_press_test(void)
{
    static uint32_t key1_press_time = 0;
    static uint8_t key1_long_press_flag = 0;
    
    // KEY1长按检测
    if (get_key_state(KEY1_PRESSED))
    {
        if (key1_press_time == 0)
        {
            key1_press_time = HAL_GetTick(); // 记录按下时间
        }
        else if ((HAL_GetTick() - key1_press_time >= 2000) && !key1_long_press_flag)
        {
            // 长按2秒触发
            key1_long_press_flag = 1;
            my_printf(&huart1, "KEY1 Long Press Detected (2s)\r\n");
            // 执行长按功能
        }
    }
    else
    {
        key1_press_time = 0;
        key1_long_press_flag = 0;
    }
}

/**
 * @brief 综合按键测试函数
 */
void comprehensive_key_test(void)
{
    // 执行各种按键测试
    key_function_test();
    key_status_display_test();
    key_combination_test();
    key_long_press_test();
}
