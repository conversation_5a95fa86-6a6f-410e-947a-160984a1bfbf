// Copyright (c) 2024 白蛋电子工作室

#include "app_key_example.h"

/**
 * @brief 按键使用示例
 */
void key_usage_example(void)
{
    // 示例1: 检测单个按键按下
    if (is_key_pressed(KEY1_PRESSED))
    {
        my_printf(&huart1, "Example: KEY1 was just pressed!\r\n");
        // 在这里添加KEY1按下时的处理代码
    }
    
    // 示例2: 检测按键释放
    if (is_key_released(KEY2_PRESSED))
    {
        my_printf(&huart1, "Example: KEY2 was just released!\r\n");
        // 在这里添加KEY2释放时的处理代码
    }
    
    // 示例3: 检测按键持续按下状态
    if (get_key_state(KEY3_PRESSED))
    {
        // KEY3正在被按下（持续状态）
        // 注意：这个会持续触发，直到按键释放
        static uint32_t last_print = 0;
        if (HAL_GetTick() - last_print > 500) // 每500ms打印一次
        {
            my_printf(&huart1, "Example: KEY3 is being held down\r\n");
            last_print = HAL_GetTick();
        }
    }
    
    // 示例4: 检测多个按键同时按下
    if (get_key_state(KEY1_PRESSED | KEY2_PRESSED) == (KEY1_PRESSED | KEY2_PRESSED))
    {
        static uint8_t combo_printed = 0;
        if (!combo_printed)
        {
            my_printf(&huart1, "Example: KEY1 and KEY2 pressed together!\r\n");
            combo_printed = 1;
        }
    }
    else
    {
        static uint8_t combo_printed = 0; // 重置标志
    }
}

/**
 * @brief 按键控制激光绘图示例
 */
void key_control_laser_example(void)
{
    // KEY1: 开始激光绘图
    if (is_key_pressed(KEY1_PRESSED))
    {
        my_printf(&huart1, "Starting laser drawing...\r\n");
        // app_laser_draw_init();
        // 可以在这里调用激光绘图初始化
    }
    
    // KEY2: 停止激光绘图
    if (is_key_pressed(KEY2_PRESSED))
    {
        my_printf(&huart1, "Stopping laser drawing...\r\n");
        // stop_drawing();
        // 可以在这里调用停止绘图
    }
    
    // KEY3: 设置激光功率
    if (is_key_pressed(KEY3_PRESSED))
    {
        static uint16_t laser_power = 10;
        laser_power += 10;
        if (laser_power > 100) laser_power = 10;
        
        my_printf(&huart1, "Setting laser power to: %d\r\n", laser_power);
        // laser_set_power(laser_power);
        // 可以在这里调用设置激光功率
    }
    
    // KEY4: 测试激光开关
    if (is_key_pressed(KEY4_PRESSED))
    {
        static uint8_t laser_on = 0;
        laser_on = !laser_on;
        
        if (laser_on)
        {
            my_printf(&huart1, "Laser ON\r\n");
            // laser_on();
        }
        else
        {
            my_printf(&huart1, "Laser OFF\r\n");
            // laser_off();
        }
    }
}

/**
 * @brief 按键控制LED示例
 */
void key_control_led_example(void)
{
    // KEY1: 控制LED4
    if (is_key_pressed(KEY1_PRESSED))
    {
        static uint8_t led4_state = 0;
        led4_state = !led4_state;
        
        HAL_GPIO_WritePin(LED4_GPIO_Port, LED4_Pin, led4_state ? GPIO_PIN_RESET : GPIO_PIN_SET);
        my_printf(&huart1, "LED4 %s\r\n", led4_state ? "ON" : "OFF");
    }
    
    // KEY2: 控制LED6
    if (is_key_pressed(KEY2_PRESSED))
    {
        static uint8_t led6_state = 0;
        led6_state = !led6_state;
        
        HAL_GPIO_WritePin(LED6_GPIO_Port, LED6_Pin, led6_state ? GPIO_PIN_RESET : GPIO_PIN_SET);
        my_printf(&huart1, "LED6 %s\r\n", led6_state ? "ON" : "OFF");
    }
    
    // KEY3: LED闪烁模式
    if (is_key_pressed(KEY3_PRESSED))
    {
        my_printf(&huart1, "LED Blink Mode\r\n");
        for (int i = 0; i < 5; i++)
        {
            HAL_GPIO_WritePin(LED4_GPIO_Port, LED4_Pin, GPIO_PIN_RESET); // LED ON
            HAL_GPIO_WritePin(LED6_GPIO_Port, LED6_Pin, GPIO_PIN_RESET); // LED ON
            HAL_Delay(200);
            HAL_GPIO_WritePin(LED4_GPIO_Port, LED4_Pin, GPIO_PIN_SET);   // LED OFF
            HAL_GPIO_WritePin(LED6_GPIO_Port, LED6_Pin, GPIO_PIN_SET);   // LED OFF
            HAL_Delay(200);
        }
    }
}

/**
 * @brief 主按键处理函数 - 在主循环中调用
 */
void main_key_handler(void)
{
    // 可以根据需要选择调用哪些示例函数
    key_usage_example();
    key_control_laser_example();
    key_control_led_example();
}
