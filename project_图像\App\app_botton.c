#include "app_botton.h"

uint8_t key_val = 0;  // ��ǰ����״̬
uint8_t key_old = 0;  // ǰһ����״̬
uint8_t key_down = 0; // ���µİ���
uint8_t key_up = 0;	  // �ͷŵİ���

uint8_t flag = 0;

uint8_t cmd[16] = {0};
/**
 * @brief ��ȡ����״̬
 *
 * �ú�����ȡ������ GPIO �����ϵİ���״̬����������Ӧ�İ�����š�
 *
 * @return ���ذ�����š�0 ��ʾû�а������£�1-4 ��ʾ��Ӧ�İ��������¡�
 */
uint8_t key_read(void)
{
	uint8_t key_value = 0;

	// 检测KEY1 (PE0) - 按键按下时为低电平（因为使用上拉电阻）
	if (HAL_GPIO_ReadPin(KEY1_GPIO_Port, KEY1_Pin) == GPIO_PIN_RESET)
		key_value |= 0x01; // 设置bit0

	// 检测KEY2 (PE1)
	if (HAL_GPIO_ReadPin(KEY2_GPIO_Port, KEY2_Pin) == GPIO_PIN_RESET)
		key_value |= 0x02; // 设置bit1

	// 检测KEY3 (PE2)
	if (HAL_GPIO_ReadPin(KEY3_GPIO_Port, KEY3_Pin) == GPIO_PIN_RESET)
		key_value |= 0x04; // 设置bit2

	// 检测KEY4 (PE3)
	if (HAL_GPIO_ReadPin(KEY4_GPIO_Port, KEY4_Pin) == GPIO_PIN_RESET)
		key_value |= 0x08; // 设置bit3

	return key_value;
}

/**
 * @brief ������������
 *
 * �ú�������ɨ�谴����״̬�������°������º��ͷŵı�־
 */
void botton_task(MultiTimer *timer, void *userData)
{

	// ��ȡ��ǰ����״̬
	key_val = key_read();
	// ���㰴�µİ�������ǰ����״̬��ǰһ״̬��򣬲��뵱ǰ״̬���룩
	key_down = key_val & (key_old ^ key_val);
	// �����ͷŵİ�������ǰδ����״̬��ǰһ״̬��򣬲���ǰһ״̬���룩
	key_up = ~key_val & (key_old ^ key_val);
	// ����ǰһ����״̬
	key_old = key_val;

	// 按键处理逻辑
	if(key_down & 0x01) // KEY1按下
	{
		my_printf(&huart1, "KEY1 Pressed\r\n");
		// 可以在这里添加KEY1的功能
	}

	if(key_down & 0x02) // KEY2按下
	{
		my_printf(&huart1, "KEY2 Pressed\r\n");
		// 可以在这里添加KEY2的功能
	}

	if(key_down & 0x04) // KEY3按下
	{
		my_printf(&huart1, "KEY3 Pressed\r\n");
		// 可以在这里添加KEY3的功能
	}

	if(key_down & 0x08) // KEY4按下
	{
		my_printf(&huart1, "KEY4 Pressed\r\n");
		// 可以在这里添加KEY4的功能
	}

	// 按键释放处理
	if(key_up & 0x01) // KEY1释放
	{
		my_printf(&huart1, "KEY1 Released\r\n");
	}

	if(key_up & 0x02) // KEY2释放
	{
		my_printf(&huart1, "KEY2 Released\r\n");
	}

	if(key_up & 0x04) // KEY3释放
	{
		my_printf(&huart1, "KEY3 Released\r\n");
	}

	if(key_up & 0x08) // KEY4释放
	{
		my_printf(&huart1, "KEY4 Released\r\n");
	}

	multiTimerStart(&mt_botton, BOTTON_TASK_TIME, botton_task, NULL);
}

/**
 * @brief 获取指定按键状态
 * @param key_mask 按键掩码 (KEY1_PRESSED, KEY2_PRESSED, KEY3_PRESSED, KEY4_PRESSED)
 * @return 按键状态 (0: 未按下, 非0: 按下)
 */
uint8_t get_key_state(uint8_t key_mask)
{
	return key_val & key_mask;
}

/**
 * @brief 判断按键是否按下
 * @param key_mask 按键掩码
 * @return 1: 按键刚按下, 0: 按键未按下
 */
uint8_t is_key_pressed(uint8_t key_mask)
{
	return key_down & key_mask;
}

/**
 * @brief 判断按键是否释放
 * @param key_mask 按键掩码
 * @return 1: 按键刚释放, 0: 按键未释放
 */
uint8_t is_key_released(uint8_t key_mask)
{
	return key_up & key_mask;
}
