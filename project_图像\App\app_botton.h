#ifndef __BOTTON_APP_H
#define __BOTTON_APP_H

#include "mydefine.h"

/* 按键功能定义 */
#define KEY1_PRESSED 0x01  // KEY1按下标志
#define KEY2_PRESSED 0x02  // KEY2按下标志
#define KEY3_PRESSED 0x04  // KEY3按下标志
#define KEY4_PRESSED 0x08  // KEY4按下标志

/* 函数声明 */
uint8_t key_read(void);                                    // 读取按键状态
void botton_task(MultiTimer *timer, void *userData);       // 按键扫描任务
uint8_t get_key_state(uint8_t key_mask);                  // 获取指定按键状态
uint8_t is_key_pressed(uint8_t key_mask);                 // 判断按键是否按下
uint8_t is_key_released(uint8_t key_mask);                // 判断按键是否释放

/* 全局变量声明 */
extern uint8_t key_val;   // 当前按键状态
extern uint8_t key_old;   // 前一次按键状态
extern uint8_t key_down;  // 按下的按键
extern uint8_t key_up;    // 释放的按键

#endif


