# 按键使用说明

## 📋 按键配置概述

项目中使用PE0-3引脚作为4个独立按键的输入，按键配置如下：

### 按键引脚定义

| 按键 | 引脚 | GPIO端口 | 功能描述 |
|------|------|----------|----------|
| KEY1 | PE0  | GPIOE    | 功能键1  |
| KEY2 | PE1  | GPIOE    | 功能键2  |
| KEY3 | PE2  | GPIOE    | 功能键3  |
| KEY4 | PE3  | GPIOE    | 功能键4  |

### 硬件配置

- **输入模式**: GPIO_MODE_INPUT
- **上拉电阻**: GPIO_PULLUP (启用内部上拉电阻)
- **触发逻辑**: 按键按下时为低电平 (GPIO_PIN_RESET)
- **扫描频率**: 100ms (由BOTTON_TASK_TIME定义)

## 🚀 使用方法

### 1. 基本按键检测

```c
#include "app_botton.h"

// 检测按键按下事件
if (is_key_pressed(KEY1_PRESSED))
{
    // KEY1刚被按下
    printf("KEY1 pressed!\n");
}

// 检测按键释放事件
if (is_key_released(KEY1_PRESSED))
{
    // KEY1刚被释放
    printf("KEY1 released!\n");
}

// 检测按键持续按下状态
if (get_key_state(KEY1_PRESSED))
{
    // KEY1正在被按下
    printf("KEY1 is being held\n");
}
```

### 2. 按键掩码定义

```c
#define KEY1_PRESSED 0x01  // KEY1按下标志
#define KEY2_PRESSED 0x02  // KEY2按下标志
#define KEY3_PRESSED 0x04  // KEY3按下标志
#define KEY4_PRESSED 0x08  // KEY4按下标志
```

### 3. 多按键组合检测

```c
// 检测两个按键同时按下
if (get_key_state(KEY1_PRESSED | KEY2_PRESSED) == (KEY1_PRESSED | KEY2_PRESSED))
{
    printf("KEY1 and KEY2 pressed together!\n");
}

// 检测任意按键按下
if (get_key_state(KEY1_PRESSED | KEY2_PRESSED | KEY3_PRESSED | KEY4_PRESSED))
{
    printf("At least one key is pressed\n");
}
```

### 4. 长按检测示例

```c
void key_long_press_detection(void)
{
    static uint32_t key1_press_time = 0;
    static uint8_t key1_long_press_flag = 0;
    
    if (get_key_state(KEY1_PRESSED))
    {
        if (key1_press_time == 0)
        {
            key1_press_time = HAL_GetTick();
        }
        else if ((HAL_GetTick() - key1_press_time >= 2000) && !key1_long_press_flag)
        {
            // 长按2秒触发
            key1_long_press_flag = 1;
            printf("KEY1 long press detected!\n");
        }
    }
    else
    {
        key1_press_time = 0;
        key1_long_press_flag = 0;
    }
}
```

## 🔧 API函数说明

### 核心函数

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `key_read()` | 读取当前按键状态 | 无 | uint8_t: 按键状态位掩码 |
| `get_key_state(mask)` | 获取指定按键状态 | uint8_t: 按键掩码 | uint8_t: 按键状态 |
| `is_key_pressed(mask)` | 判断按键是否刚按下 | uint8_t: 按键掩码 | uint8_t: 1=刚按下, 0=未按下 |
| `is_key_released(mask)` | 判断按键是否刚释放 | uint8_t: 按键掩码 | uint8_t: 1=刚释放, 0=未释放 |

### 全局变量

| 变量名 | 类型 | 功能描述 |
|--------|------|----------|
| `key_val` | uint8_t | 当前按键状态 |
| `key_old` | uint8_t | 前一次按键状态 |
| `key_down` | uint8_t | 按下的按键标志 |
| `key_up` | uint8_t | 释放的按键标志 |

## 📝 使用示例

### 示例1: 控制LED

```c
void key_control_led(void)
{
    if (is_key_pressed(KEY1_PRESSED))
    {
        // 切换LED4状态
        static uint8_t led_state = 0;
        led_state = !led_state;
        HAL_GPIO_WritePin(LED4_GPIO_Port, LED4_Pin, 
                         led_state ? GPIO_PIN_RESET : GPIO_PIN_SET);
    }
}
```

### 示例2: 控制激光绘图

```c
void key_control_laser(void)
{
    if (is_key_pressed(KEY1_PRESSED))
    {
        app_laser_draw_init();  // 初始化激光绘图
    }
    
    if (is_key_pressed(KEY2_PRESSED))
    {
        stop_drawing();         // 停止绘图
    }
    
    if (is_key_pressed(KEY3_PRESSED))
    {
        laser_on();             // 开启激光
    }
    
    if (is_key_pressed(KEY4_PRESSED))
    {
        laser_off();            // 关闭激光
    }
}
```

## ⚠️ 注意事项

1. **按键扫描**: 按键扫描在`botton_task`中进行，每100ms扫描一次
2. **防抖处理**: 系统自动进行按键防抖处理
3. **中断安全**: 按键检测函数可以在中断中安全调用
4. **内存占用**: 按键状态仅占用4个字节的全局变量
5. **实时性**: 按键响应延迟最大为100ms

## 🔄 集成到主程序

在主程序中使用按键功能：

```c
#include "app_botton.h"
#include "app_key_example.h"

int main(void)
{
    // 系统初始化
    HAL_Init();
    // ... 其他初始化
    
    // 启动按键扫描任务
    multiTimerStart(&mt_botton, BOTTON_TASK_TIME, botton_task, NULL);
    
    while (1)
    {
        // 在主循环中处理按键事件
        main_key_handler();
        
        // 其他任务处理
        multiTimerYield();
    }
}
```

这样就完成了PE0-3按键的完整配置和使用！
