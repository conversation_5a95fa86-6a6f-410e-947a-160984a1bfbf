// Copyright (c) 2024 白蛋电子工作室

#ifndef __APP_MAIXCAM_H
#define __APP_MAIXCAM_H

#include "mydefine.h"

// 激光类型标识符
#define TO_LASER_ID 'T'   // to(x,y)格式标识符
#define PUR_LASER_ID 'P'  // pur(x,y)格式标识符

// 激光坐标结构体定义
#ifndef LASERCOORD_T_DEFINED
#define LASERCOORD_T_DEFINED
typedef struct
{
    char type; // 激光类型: 'T'表示to类型, 'P'表示pur类型
    int x;     // X坐标
    int y;     // Y坐标
} LaserCoord_t;
#endif

// 激光坐标回调函数类型
typedef void (*LaserCoordCallback_t)(LaserCoord_t coord);

// MaixCam数据处理任务
void maixcam_task(MultiTimer *timer, void *userData);

// 数据解析函数
int maixcam_parse_data(char *buffer);

// 设置激光坐标回调函数
void maixcam_set_callback(LaserCoordCallback_t callback);

#endif /* __APP_MAIXCAM_H */
