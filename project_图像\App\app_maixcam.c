// Copyright (c) 2024 白蛋电子工作室

#include "app_maixcam.h"

// 默认激光坐标回调函数
static void default_laser_callback(LaserCoord_t coord)
{
    // 根据激光类型处理不同的逻辑
    if (coord.type == TO_LASER_ID)
    {
        // 初始化PID控制器
        app_pid_init();
        // 设置PID目标位置
        app_pid_set_target(160, 120);
        // 打印坐标信息
        my_printf(&huart1, "to: X=%d, Y=%d\r\n", coord.x, coord.y);
        // 启动PID控制
        app_pid_start();
    }
    else if (coord.type == PUR_LASER_ID)
    {
        my_printf(&huart1, "pur: X=%d, Y=%d\r\n", coord.x, coord.y);
    }
}

// 激光坐标回调函数指针
static LaserCoordCallback_t laser_coord_callback = default_laser_callback;

// 设置激光坐标回调函数
void maixcam_set_callback(LaserCoordCallback_t callback)
{
    if (callback != NULL)
        laser_coord_callback = callback;
    else
        laser_coord_callback = default_laser_callback;
}

// MaixCam数据解析函数，支持to(x,y)和pur(x,y)格式
int maixcam_parse_data(char *buffer)
{
    if (!buffer)
        return -1; // 缓冲区为空

    LaserCoord_t coord;

    // 解析to(x,y)格式
    if (strncmp(buffer, "to(", 3) == 0)
    {
        int parsed = sscanf(buffer, "to(%d,%d)", &coord.x, &coord.y);
        if (parsed == 2)
        {
            coord.type = TO_LASER_ID;
            // 调用回调函数处理坐标
            if (laser_coord_callback)
                laser_coord_callback(coord);
            return 0; // 解析成功
        }
        return -2; // to格式解析失败
    }

    // 解析pur(x,y)格式
    if (strncmp(buffer, "pur(", 4) == 0)
    {
        int parsed = sscanf(buffer, "pur(%d,%d)", &coord.x, &coord.y);
        if (parsed == 2)
        {
            coord.type = PUR_LASER_ID;
            // 调用回调函数处理坐标
            if (laser_coord_callback)
                laser_coord_callback(coord);
            return 0; // 解析成功
        }
        return -3; // pur格式解析失败
    }

    return -4; // 未识别的格式
}

// MaixCam数据处理任务函数
void maixcam_task(MultiTimer *timer, void *userData)
{
    int length_cam = rt_ringbuffer_data_len(&ringbuffer_cam);
    if (length_cam > 0)
    {
        rt_ringbuffer_get(&ringbuffer_cam, output_buffer_cam, length_cam);
        output_buffer_cam[length_cam] = '\0';

        // 解析激光坐标数据
        int result = maixcam_parse_data((char *)output_buffer_cam);

        memset(output_buffer_cam, 0, length_cam);
    }

    // 可选择性启动PID任务
    // multiTimerStart(&mt_pid, 10, app_pid_task, NULL);
}
